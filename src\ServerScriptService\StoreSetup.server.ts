// Server script to set up Store objects with ProximityPrompts in Workspace

import { STORE_CONSTANTS, StoreData } from "ReplicatedStorage/StoreTypes";

// Services
const Workspace = game.GetService("Workspace");
const ReplicatedStorage = game.GetService("ReplicatedStorage");
const HttpService = game.GetService("HttpService");

// Sample store data
const SAMPLE_STORE_DATA: StoreData = {
	storeName: "General Store",
	items: [
		{
			id: "sword",
			name: "Iron Sword",
			description: "A sturdy iron sword for combat",
			price: 100,
		},
		{
			id: "potion",
			name: "Health Potion",
			description: "Restores 50 health points",
			price: 25,
		},
		{
			id: "armor",
			name: "Leather Armor",
			description: "Basic protection from attacks",
			price: 75,
		},
	],
};

function createStoreObject(): Model {
	// Create the main store model
	const store = new Instance("Model");
	store.Name = "Store";
	store.Parent = Workspace;

	// Create the store building (a simple part for now)
	const storePart = new Instance("Part");
	storePart.Name = "StorePart";
	storePart.Size = new Vector3(10, 8, 10);
	storePart.Position = new Vector3(0, 4, 0);
	storePart.Anchored = true;
	storePart.BrickColor = new BrickColor("Bright blue");
	storePart.Material = Enum.Material.Neon;
	storePart.Parent = store;

	// Create a ProximityPrompt
	const proximityPrompt = new Instance("ProximityPrompt");
	proximityPrompt.Name = "StorePrompt";
	proximityPrompt.ActionText = STORE_CONSTANTS.PROXIMITY_PROMPT_TEXT;
	proximityPrompt.MaxActivationDistance = STORE_CONSTANTS.PROXIMITY_PROMPT_DISTANCE;
	proximityPrompt.RequiresLineOfSight = false;
	proximityPrompt.Parent = storePart;

	// Store the store data in the model for easy access
	const storeDataValue = new Instance("StringValue");
	storeDataValue.Name = "StoreData";
	storeDataValue.Value = HttpService.JSONEncode(SAMPLE_STORE_DATA);
	storeDataValue.Parent = store;

	// Add a unique identifier for this store
	const storeIdValue = new Instance("StringValue");
	storeIdValue.Name = "StoreId";
	storeIdValue.Value = "general_store_1";
	storeIdValue.Parent = store;

	print(`Created store: ${store.Name} at position ${storePart.Position}`);
	return store;
}

// Create the store when the server starts
const store = createStoreObject();

// Handle ProximityPrompt interactions
const storePart = store.FindFirstChild("StorePart") as Part;
const proximityPrompt = storePart?.FindFirstChild("StorePrompt") as ProximityPrompt;

if (proximityPrompt) {
	proximityPrompt.Triggered.Connect((player: Player) => {
		print(`Player ${player.Name} triggered the store prompt`);
		
		// Get store data
		const storeDataValue = store.FindFirstChild("StoreData") as StringValue;
		const storeIdValue = store.FindFirstChild("StoreId") as StringValue;
		
		if (storeDataValue && storeIdValue) {
			// Fire a remote event to the client to open the store UI
			const remoteEvents = ReplicatedStorage.WaitForChild("RemoteEvents") as Folder;
			const openStoreEvent = remoteEvents.WaitForChild("OpenStore") as RemoteEvent;

			openStoreEvent.FireClient(player, storeIdValue.Value, storeDataValue.Value);
		}
	});
}
