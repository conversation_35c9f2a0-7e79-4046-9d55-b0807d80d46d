import { make<PERSON><PERSON> } from "ReplicatedStorage/module";
import { StoreUI } from "./StoreUI";
import { StoreData } from "ReplicatedStorage/StoreTypes";

print(makeHello("main.client.ts"));

// Initialize the Store UI system
const storeUI = new StoreUI();

// Get services
const ReplicatedStorage = game.GetService("ReplicatedStorage");
const HttpService = game.GetService("HttpService");
const UserInputService = game.GetService("UserInputService");

// Wait for RemoteEvents to be created
const remoteEvents = ReplicatedStorage.WaitForChild("RemoteEvents") as Folder;
const openStoreEvent = remoteEvents.WaitForChild("OpenStore") as RemoteEvent;

// Handle store opening from server
openStoreEvent.OnClientEvent.Connect((storeId: string, storeDataJson: string) => {
	print(`Received request to open store: ${storeId}`);

	try {
		const storeData = HttpService.JSONDecode(storeDataJson) as StoreData;
		storeUI.openStore(storeData, storeId);
	} catch (error) {
		warn(`Failed to parse store data: ${error}`);
	}
});

// Handle ESC key to close store
UserInputService.InputBegan.Connect((input: InputObject, gameProcessed: boolean) => {
	if (gameProcessed) return;

	if (input.KeyCode === Enum.KeyCode.Escape && storeUI.isStoreOpen()) {
		storeUI.closeStore();
	}
});

print("Store system initialized on client");
