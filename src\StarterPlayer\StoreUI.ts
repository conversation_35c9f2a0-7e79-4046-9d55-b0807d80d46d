// Client script to create and manage Store UI

import { StoreData, StoreItem, STORE_CONSTANTS } from "ReplicatedStorage/StoreTypes";

class StoreUI {
	private screenGui: ScreenGui | undefined;
	private mainFrame: Frame | undefined;
	private isOpen = false;
	private currentStoreId = "";

	// Services
	private Players = game.GetService("Players");
	private TweenService = game.GetService("TweenService");
	private ReplicatedStorage = game.GetService("ReplicatedStorage");

	constructor() {
		this.createUI();
	}

	private createUI(): void {
		const playerGui = this.Players.LocalPlayer?.WaitForChild("PlayerGui") as PlayerGui;
		
		// Create ScreenGui
		this.screenGui = new Instance("ScreenGui");
		this.screenGui.Name = "StoreUI";
		this.screenGui.ResetOnSpawn = false;
		this.screenGui.Parent = playerGui;

		// Create main frame
		this.mainFrame = new Instance("Frame");
		this.mainFrame.Name = "MainFrame";
		this.mainFrame.Size = new UDim2(0, 600, 0, 400);
		this.mainFrame.Position = new UDim2(0.5, -300, 0.5, -200);
		this.mainFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50);
		this.mainFrame.BorderSizePixel = 0;
		this.mainFrame.Visible = false;
		this.mainFrame.Parent = this.screenGui;

		// Add corner radius
		const corner = new Instance("UICorner");
		corner.CornerRadius = new UDim(0, 12);
		corner.Parent = this.mainFrame;

		// Create title bar
		const titleBar = new Instance("Frame");
		titleBar.Name = "TitleBar";
		titleBar.Size = new UDim2(1, 0, 0, 50);
		titleBar.Position = new UDim2(0, 0, 0, 0);
		titleBar.BackgroundColor3 = Color3.fromRGB(30, 30, 30);
		titleBar.BorderSizePixel = 0;
		titleBar.Parent = this.mainFrame;

		const titleCorner = new Instance("UICorner");
		titleCorner.CornerRadius = new UDim(0, 12);
		titleCorner.Parent = titleBar;

		// Store title
		const titleLabel = new Instance("TextLabel");
		titleLabel.Name = "TitleLabel";
		titleLabel.Size = new UDim2(1, -100, 1, 0);
		titleLabel.Position = new UDim2(0, 20, 0, 0);
		titleLabel.BackgroundTransparency = 1;
		titleLabel.Text = "Store";
		titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255);
		titleLabel.TextScaled = true;
		titleLabel.Font = Enum.Font.GothamBold;
		titleLabel.TextXAlignment = Enum.TextXAlignment.Left;
		titleLabel.Parent = titleBar;

		// Close button
		const closeButton = new Instance("TextButton");
		closeButton.Name = "CloseButton";
		closeButton.Size = new UDim2(0, 40, 0, 40);
		closeButton.Position = new UDim2(1, -50, 0, 5);
		closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50);
		closeButton.BorderSizePixel = 0;
		closeButton.Text = "X";
		closeButton.TextColor3 = Color3.fromRGB(255, 255, 255);
		closeButton.TextScaled = true;
		closeButton.Font = Enum.Font.GothamBold;
		closeButton.Parent = titleBar;

		const closeCorner = new Instance("UICorner");
		closeCorner.CornerRadius = new UDim(0, 8);
		closeCorner.Parent = closeButton;

		// Content area
		const contentFrame = new Instance("ScrollingFrame");
		contentFrame.Name = "ContentFrame";
		contentFrame.Size = new UDim2(1, -20, 1, -70);
		contentFrame.Position = new UDim2(0, 10, 0, 60);
		contentFrame.BackgroundTransparency = 1;
		contentFrame.BorderSizePixel = 0;
		contentFrame.ScrollBarThickness = 8;
		contentFrame.Parent = this.mainFrame;

		// Grid layout for items
		const gridLayout = new Instance("UIGridLayout");
		gridLayout.CellSize = new UDim2(0, 180, 0, 120);
		gridLayout.CellPadding = new UDim2(0, 10, 0, 10);
		gridLayout.SortOrder = Enum.SortOrder.Name;
		gridLayout.Parent = contentFrame;

		// Connect close button
		closeButton.MouseButton1Click.Connect(() => {
			this.closeStore();
		});
	}

	public openStore(storeData: StoreData, storeId?: string): void {
		if (!this.mainFrame || this.isOpen) return;

		this.isOpen = true;
		this.currentStoreId = storeId || "unknown_store";
		
		// Update title
		const titleLabel = this.mainFrame.FindFirstChild("TitleBar")?.FindFirstChild("TitleLabel") as TextLabel;
		if (titleLabel) {
			titleLabel.Text = storeData.storeName;
		}

		// Clear existing items
		const contentFrame = this.mainFrame.FindFirstChild("ContentFrame") as ScrollingFrame;
		if (contentFrame) {
			contentFrame.GetChildren().forEach(child => {
				if (child.IsA("Frame")) {
					child.Destroy();
				}
			});

			// Create item frames
			storeData.items.forEach(item => {
				this.createItemFrame(item, contentFrame);
			});

			// Update canvas size
			const gridLayout = contentFrame.FindFirstChild("UIGridLayout") as UIGridLayout;
			if (gridLayout) {
				gridLayout.GetPropertyChangedSignal("AbsoluteContentSize").Connect(() => {
					contentFrame.CanvasSize = new UDim2(0, 0, 0, gridLayout.AbsoluteContentSize.Y);
				});
			}
		}

		// Show the UI with animation
		this.mainFrame.Visible = true;
		this.mainFrame.Size = new UDim2(0, 0, 0, 0);
		
		const tweenInfo = new TweenInfo(STORE_CONSTANTS.UI_TWEEN_TIME, Enum.EasingStyle.Back, Enum.EasingDirection.Out);
		const tween = this.TweenService.Create(this.mainFrame, tweenInfo, {
			Size: new UDim2(0, 600, 0, 400)
		});
		tween.Play();
	}

	public closeStore(): void {
		if (!this.mainFrame || !this.isOpen) return;

		this.isOpen = false;

		// Hide the UI with animation
		const tweenInfo = new TweenInfo(STORE_CONSTANTS.UI_TWEEN_TIME, Enum.EasingStyle.Back, Enum.EasingDirection.In);
		const tween = this.TweenService.Create(this.mainFrame, tweenInfo, {
			Size: new UDim2(0, 0, 0, 0)
		});
		
		tween.Play();
		tween.Completed.Connect(() => {
			this.mainFrame!.Visible = false;
		});

		// Fire close event to server
		const remoteEvents = this.ReplicatedStorage.WaitForChild("RemoteEvents") as Folder;
		const closeStoreEvent = remoteEvents.WaitForChild("CloseStore") as RemoteEvent;
		closeStoreEvent.FireServer();
	}

	private createItemFrame(item: StoreItem, parent: ScrollingFrame): void {
		const itemFrame = new Instance("Frame");
		itemFrame.Name = `Item_${item.id}`;
		itemFrame.BackgroundColor3 = Color3.fromRGB(70, 70, 70);
		itemFrame.BorderSizePixel = 0;
		itemFrame.Parent = parent;

		const itemCorner = new Instance("UICorner");
		itemCorner.CornerRadius = new UDim(0, 8);
		itemCorner.Parent = itemFrame;

		// Item name
		const nameLabel = new Instance("TextLabel");
		nameLabel.Name = "NameLabel";
		nameLabel.Size = new UDim2(1, -10, 0, 25);
		nameLabel.Position = new UDim2(0, 5, 0, 5);
		nameLabel.BackgroundTransparency = 1;
		nameLabel.Text = item.name;
		nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255);
		nameLabel.TextScaled = true;
		nameLabel.Font = Enum.Font.GothamBold;
		nameLabel.Parent = itemFrame;

		// Item description
		const descLabel = new Instance("TextLabel");
		descLabel.Name = "DescLabel";
		descLabel.Size = new UDim2(1, -10, 0, 40);
		descLabel.Position = new UDim2(0, 5, 0, 30);
		descLabel.BackgroundTransparency = 1;
		descLabel.Text = item.description;
		descLabel.TextColor3 = Color3.fromRGB(200, 200, 200);
		descLabel.TextScaled = true;
		descLabel.Font = Enum.Font.Gotham;
		descLabel.TextWrapped = true;
		descLabel.Parent = itemFrame;

		// Price label
		const priceLabel = new Instance("TextLabel");
		priceLabel.Name = "PriceLabel";
		priceLabel.Size = new UDim2(0.5, -5, 0, 20);
		priceLabel.Position = new UDim2(0, 5, 1, -25);
		priceLabel.BackgroundTransparency = 1;
		priceLabel.Text = `$${item.price}`;
		priceLabel.TextColor3 = Color3.fromRGB(100, 255, 100);
		priceLabel.TextScaled = true;
		priceLabel.Font = Enum.Font.GothamBold;
		priceLabel.Parent = itemFrame;

		// Buy button
		const buyButton = new Instance("TextButton");
		buyButton.Name = "BuyButton";
		buyButton.Size = new UDim2(0.5, -5, 0, 20);
		buyButton.Position = new UDim2(0.5, 0, 1, -25);
		buyButton.BackgroundColor3 = Color3.fromRGB(50, 150, 50);
		buyButton.BorderSizePixel = 0;
		buyButton.Text = "Buy";
		buyButton.TextColor3 = Color3.fromRGB(255, 255, 255);
		buyButton.TextScaled = true;
		buyButton.Font = Enum.Font.GothamBold;
		buyButton.Parent = itemFrame;

		const buyCorner = new Instance("UICorner");
		buyCorner.CornerRadius = new UDim(0, 4);
		buyCorner.Parent = buyButton;

		// Handle buy button click
		buyButton.MouseButton1Click.Connect(() => {
			print(`Attempting to buy ${item.name}`);
			
			// Fire purchase event to server
			const remoteEvents = this.ReplicatedStorage.WaitForChild("RemoteEvents") as Folder;
			const purchaseItemEvent = remoteEvents.WaitForChild("PurchaseItem") as RemoteEvent;
			
			// Use the current store ID
			purchaseItemEvent.FireServer(this.currentStoreId, item.id);
		});
	}

	public isStoreOpen(): boolean {
		return this.isOpen;
	}
}

// Export the StoreUI class for use in the main client script
export { StoreUI };
