// Shared types and interfaces for the Store system

export interface StoreItem {
	id: string;
	name: string;
	description: string;
	price: number;
	icon?: string;
}

export interface StoreData {
	storeName: string;
	items: StoreItem[];
}

// Events for client-server communication
export interface StoreEvents {
	OpenStore: (player: Player, storeId: string) => void;
	CloseStore: (player: Player) => void;
	PurchaseItem: (player: Player, storeId: string, itemId: string) => void;
}

// Constants
export const STORE_CONSTANTS = {
	PROXIMITY_PROMPT_TEXT: "Open Store",
	PROXIMITY_PROMPT_DISTANCE: 10,
	UI_TWEEN_TIME: 0.3,
} as const;
