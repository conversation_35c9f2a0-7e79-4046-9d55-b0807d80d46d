{"name": "roblox-ts-game", "globIgnorePaths": ["**/package.json", "**/tsconfig.json"], "tree": {"$className": "DataModel", "ServerScriptService": {"$className": "ServerScriptService", "TS": {"$path": "out/ServerScriptService"}}, "ReplicatedStorage": {"$className": "ReplicatedStorage", "rbxts_include": {"$path": "include", "node_modules": {"$className": "Folder", "@rbxts": {"$path": "node_modules/@rbxts"}}}, "TS": {"$path": "out/ReplicatedStorage"}}, "StarterPlayer": {"$className": "StarterPlayer", "StarterPlayerScripts": {"$className": "StarterPlayerScripts", "TS": {"$path": "out/StarterPlayer"}}}, "Workspace": {"$className": "Workspace", "$properties": {"FilteringEnabled": true}}, "HttpService": {"$className": "HttpService", "$properties": {"HttpEnabled": true}}, "SoundService": {"$className": "SoundService", "$properties": {"RespectFilteringEnabled": true}}}}