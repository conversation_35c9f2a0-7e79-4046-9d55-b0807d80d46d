// Server script to set up RemoteEvents for Store system

// Services
const ReplicatedStorage = game.GetService("ReplicatedStorage");

// Create RemoteEvents folder if it doesn't exist
let remoteEventsFolder = ReplicatedStorage.FindFirstChild("RemoteEvents") as Folder;
if (!remoteEventsFolder) {
	remoteEventsFolder = new Instance("Folder");
	remoteEventsFolder.Name = "RemoteEvents";
	remoteEventsFolder.Parent = ReplicatedStorage;
}

// Create OpenStore RemoteEvent
let openStoreEvent = remoteEventsFolder.FindFirstChild("OpenStore") as RemoteEvent;
if (!openStoreEvent) {
	openStoreEvent = new Instance("RemoteEvent");
	openStoreEvent.Name = "OpenStore";
	openStoreEvent.Parent = remoteEventsFolder;
}

// Create CloseStore RemoteEvent
let closeStoreEvent = remoteEventsFolder.FindFirstChild("CloseStore") as RemoteEvent;
if (!closeStoreEvent) {
	closeStoreEvent = new Instance("RemoteEvent");
	closeStoreEvent.Name = "CloseStore";
	closeStoreEvent.Parent = remoteEventsFolder;
}

// Create PurchaseItem RemoteEvent
let purchaseItemEvent = remoteEventsFolder.FindFirstChild("PurchaseItem") as RemoteEvent;
if (!purchaseItemEvent) {
	purchaseItemEvent = new Instance("RemoteEvent");
	purchaseItemEvent.Name = "PurchaseItem";
	purchaseItemEvent.Parent = remoteEventsFolder;
}

print("RemoteEvents setup complete");

// Handle purchase requests from clients
purchaseItemEvent.OnServerEvent.Connect((player: Player, ...args: unknown[]) => {
	// Type-safe parameter extraction with validation
	const storeId = args[0] as string;
	const itemId = args[1] as string;

	// Validate that we received the expected parameters
	if (typeOf(storeId) !== "string" || typeOf(itemId) !== "string") {
		warn(`Invalid purchase request from ${player.Name}: expected string parameters`);
		return;
	}

	print(`Player ${player.Name} wants to purchase item ${itemId} from store ${storeId}`);
	// Here you would implement the actual purchase logic
	// For now, we'll just acknowledge the purchase
	// You could check player currency, deduct cost, give item, etc.
});
